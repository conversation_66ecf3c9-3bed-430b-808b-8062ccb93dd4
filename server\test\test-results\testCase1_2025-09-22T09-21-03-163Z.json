{"testCase": "testCase1", "description": "找出连续三年净资产收益率大于15%,连续5年净利润增长率大于5%,且当前市盈率小于20,股息率大于3%的股票", "timestamp": "2025-09-22T09:21:03.163Z", "result": {"success": true, "userInput": "找出连续三年净资产收益率大于15%,连续5年净利润增长率大于5%,且当前市盈率小于20,股息率大于3%的股票", "logicAnalysis": {"needSplit": false, "reason": "所有条件都是简单的多期数据筛选和单期数值比较，可以通过直接的数据库查询实现", "singleLogic": {"description": "筛选连续三年净资产收益率大于15%、连续五年净利润增长率大于5%、当前市盈率小于20且股息率大于3%的股票", "type": "simple_filter", "complexity": "low"}}, "executableScripts": [{"logicIndex": 1, "logicDescription": "筛选连续三年净资产收益率大于15%、连续五年净利润增长率大于5%、当前市盈率小于20且股息率大于3%的股票", "logicType": "simple_filter", "complexity": "low", "scriptCode": "const executeLogic = async () => {\n  try {\n    const currentYear = new Date().getFullYear();\n    const years = [currentYear - 1, currentYear - 2, currentYear - 3];\n    const profitYears = [currentYear - 1, currentYear - 2, currentYear - 3, currentYear - 4, currentYear - 5];\n\n    const stocksWithROE = await FinancialData.aggregate([\n      {\n        $match: {\n          fiscalYear: { $in: years },\n          \"data.keyMetrics.returnOnEquity\": { $gt: 15 }\n        }\n      },\n      {\n        $group: {\n          _id: \"$stockCode\",\n          roeCount: { $sum: 1 }\n        }\n      },\n      {\n        $match: {\n          roeCount: 3\n        }\n      }\n    ]);\n\n    const stocksWithProfitGrowth = await FinancialData.aggregate([\n      {\n        $match: {\n          fiscalYear: { $in: profitYears },\n          \"data.keyMetrics.netProfitGrowthRate\": { $gt: 5 }\n        }\n      },\n      {\n        $group: {\n          _id: \"$stockCode\",\n          profitGrowthCount: { $sum: 1 }\n        }\n      },\n      {\n        $match: {\n          profitGrowthCount: 5\n        }\n      }\n    ]);\n\n    const roeStockCodes = stocksWithROE.map(stock => stock._id);\n    const profitGrowthStockCodes = stocksWithProfitGrowth.map(stock => stock._id);\n    \n    const validStockCodes = roeStockCodes.filter(code => \n      profitGrowthStockCodes.includes(code)\n    );\n\n    const result = await DailyStockData.find({\n      stockCode: { $in: validStockCodes },\n      peRatioTTM: { $lt: 20 }\n    }).populate({\n      path: 'stockCode',\n      match: { \n        stockCode: { $in: validStockCodes }\n      },\n      select: 'stockName board industry'\n    });\n\n    return result.filter(stock => stock !== null);\n  } catch (error) {\n    throw new Error(`查询失败: ${error.message}`);\n  }\n};", "generatedAt": "2025-09-22T09:21:03.161Z"}], "timestamp": "2025-09-22T09:21:03.162Z"}}