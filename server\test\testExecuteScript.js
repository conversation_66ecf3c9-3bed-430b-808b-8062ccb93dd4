const { executeScript } = require('../src/services/filter/executeScriptService');

/**
 * 测试执行脚本服务
 */
const testExecuteScript = async () => {
  try {
    // 首先加载环境变量
    require('dotenv').config({ path: require('path').join(__dirname, '../.env') });
    console.log('MONGODB_URI:', process.env.MONGODB_URI ? '已设置' : '未设置');

    console.log('开始测试执行脚本服务...\n');

    // 测试用例1：简单的连续年度筛选脚本
    const testScript1 = `
    const executeLogic = async () => {
    try {
      const currentYear = new Date().getFullYear();
      const years = [currentYear - 1, currentYear - 2, currentYear - 3];
      const profitYears = [currentYear - 1, currentYear - 2, currentYear - 3, currentYear - 4, currentYear - 5];

      const stocksWithROE = await FinancialData.aggregate([
        {
          $match: {
            fiscalYear: { $in: years },
            "data.keyMetrics.returnOnEquity": { $gt: 15 }
          }
        },
        {
          $group: {
            _id: "$stockCode",
            roeCount: { $sum: 1 }
          }
        },
        {
          $match: {
            roeCount: 3
          }
        }
      ]);

      const stocksWithProfitGrowth = await FinancialData.aggregate([
        {
          $match: {
            fiscalYear: { $in: profitYears },
            "data.keyMetrics.netProfitGrowthRate": { $gt: 5 }
          }
        },
        {
          $group: {
            _id: "$stockCode",
            profitGrowthCount: { $sum: 1 }
          }
        },
        {
          $match: {
            profitGrowthCount: 5
          }
        }
      ]);

      const roeStockCodes = stocksWithROE.map(stock => stock._id);
      const profitGrowthStockCodes = stocksWithProfitGrowth.map(stock => stock._id);
      
      const validStockCodes = roeStockCodes.filter(code => 
        profitGrowthStockCodes.includes(code)
      );

      const result = await DailyStockData.find({
        stockCode: { $in: validStockCodes },
        peRatioTTM: { $lt: 20 }
      }).populate({
        path: 'stockCode',
        match: { 
          stockCode: { $in: validStockCodes }
        },
        select: 'stockName board industry'
      });

      return result.filter(stock => stock !== null);
    } catch (error) {
      throw new Error(\`查询失败: ${error.message}\`);
    }
};
    `;
    console.log('测试用例1: 简单的连续年度筛选脚本');
    const result1 = await executeScript(testScript1);
    console.log('执行结果1:', {
      success: result1.success,
      resultCount: result1.resultCount,
      executedAt: result1.executedAt,
      sampleData: result1.result.slice(0, 3) // 只显示前3条数据
    });
    console.log('\n' + '='.repeat(80) + '\n');

    // 测试用例2：简单的市盈率筛选脚本
    //     const testScript2 = `const executeLogic = async () => {
    //   try {
    //     const result = await DailyStockData.find({
    //       peRatioTTM: { $gte: 10, $lte: 20 },
    //       totalMarketCap: { $gt: 100 }
    //     }).limit(10);

    //     return result;
    //   } catch (error) {
    //     throw new Error(\`查询失败: \${error.message}\`);
    //   }
    // };`;

    //     console.log('测试用例2: 市盈率10-20倍，市值超过100亿');
    //     const result2 = await executeScript(testScript2);
    //     console.log('执行结果2:', {
    //       success: result2.success,
    //       resultCount: result2.resultCount,
    //       executedAt: result2.executedAt,
    //       sampleData: result2.result.slice(0, 2) // 只显示前2条数据
    //     });
    //     console.log('\n' + '='.repeat(80) + '\n');

    // 测试用例3：测试安全性 - 应该被拒绝
    //     const dangerousScript = `const executeLogic = async () => {
    //   const fs = require('fs');
    //   return fs.readFileSync('/etc/passwd');
    // };`;

    //     console.log('测试用例3: 危险脚本（应该被拒绝）');
    //     try {
    //       await executeScript(dangerousScript);
    //       console.log('错误：危险脚本未被拒绝！');
    //     } catch (error) {
    //       console.log('正确：危险脚本被成功拒绝 -', error.message);
    //     }

    //     console.log('\n测试完成！');

  } catch (error) {
    console.error('测试失败:', error.message);
  }
};

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testExecuteScript();
}

module.exports = {
  testExecuteScript
};
