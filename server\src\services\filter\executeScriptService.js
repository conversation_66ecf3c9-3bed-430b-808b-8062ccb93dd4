const vm = require('vm');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');
const mongoose = require('mongoose');

/**
 * 执行AI生成的脚本代码服务
 * @param {string} scriptCode 要执行的JavaScript代码字符串
 * @returns {Promise<Object>} 执行结果
 */
const executeScript = async (scriptCode) => {
  try {
    console.log('开始执行AI生成的脚本代码...');
    
    // 验证输入
    if (!scriptCode || typeof scriptCode !== 'string') {
      throw new Error('脚本代码不能为空且必须是字符串');
    }

    // 确保数据库连接
    await ensureDatabaseConnection();

    // 安全性检查
    validateScriptSafety(scriptCode);

    // 执行脚本代码
    const result = await executeScriptInSandbox(scriptCode);

    return {
      success: true,
      result: result,
      executedAt: new Date().toISOString(),
      resultCount: Array.isArray(result) ? result.length : 1
    };

  } catch (error) {
    console.error('执行脚本代码失败:', error);
    throw new Error(`执行脚本代码失败: ${error.message}`);
  }
};

/**
 * 确保数据库连接
 */
const ensureDatabaseConnection = async () => {
  if (mongoose.connection.readyState === 0) {
    // 如果没有连接，尝试使用现有的数据库配置
    const connectDB = require('../../config/database');
    await connectDB();
  } else if (mongoose.connection.readyState === 1) {
    console.log('数据库连接正常');
  } else if (mongoose.connection.readyState === 2) {
    // 正在连接中，等待连接完成
    console.log('数据库正在连接中，等待连接完成...');
    await new Promise((resolve, reject) => {
      mongoose.connection.once('connected', resolve);
      mongoose.connection.once('error', reject);
      setTimeout(() => reject(new Error('数据库连接超时')), 10000);
    });
  } else {
    throw new Error('数据库连接状态异常');
  }
};

/**
 * 验证脚本安全性
 * @param {string} scriptCode 脚本代码
 */
const validateScriptSafety = (scriptCode) => {
  // 检查危险操作
  const dangerousPatterns = [
    /require\s*\(/,           // 禁止require其他模块
    /import\s+/,              // 禁止import
    /process\./,              // 禁止访问process对象
    /global\./,               // 禁止访问global对象
    /eval\s*\(/,              // 禁止eval
    /Function\s*\(/,          // 禁止Function构造器
    /fs\./,                   // 禁止文件系统操作
    /child_process/,          // 禁止子进程
    /exec\s*\(/,              // 禁止执行系统命令
    /spawn\s*\(/,             // 禁止spawn
    /\.\.\/\.\.\//,           // 禁止路径遍历
    /delete\s+/,              // 禁止delete操作
    /while\s*\(\s*true\s*\)/, // 禁止无限循环
    /for\s*\(\s*;\s*;\s*\)/   // 禁止无限for循环
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(scriptCode)) {
      throw new Error(`脚本包含不安全的操作: ${pattern.source}`);
    }
  }

  // 检查是否包含必要的函数结构
  if (!scriptCode.includes('executeLogic') && !scriptCode.includes('async')) {
    throw new Error('脚本必须包含executeLogic函数或async函数');
  }

  // 检查代码长度限制
  if (scriptCode.length > 10000) {
    throw new Error('脚本代码过长，超过安全限制');
  }
};

/**
 * 在沙箱环境中执行脚本
 * @param {string} scriptCode 脚本代码
 * @returns {Promise<any>} 执行结果
 */
const executeScriptInSandbox = async (scriptCode) => {
  try {
    // 创建安全的执行上下文
    const sandbox = {
      // 提供数据模型
      DailyStockData: DailyStockData,
      FinancialData: FinancialData,
      StockBasicInfo: StockBasicInfo,

      // 提供必要的全局对象
      console: {
        log: console.log,
        error: console.error,
        warn: console.warn
      },

      // 提供Date对象
      Date: Date,

      // 提供Promise
      Promise: Promise,

      // 提供setTimeout（限制时间）
      setTimeout: (fn, delay) => {
        if (delay > 30000) delay = 30000; // 最大30秒
        return setTimeout(fn, delay);
      },

      // 提供Error构造器
      Error: Error
    };

    // 创建VM上下文
    const context = vm.createContext(sandbox);

    // 包装代码，使用Promise来处理异步执行
    const wrappedCode = `
      (async () => {
        ${scriptCode}

        // 尝试执行函数
        if (typeof executeLogic === 'function') {
          return await executeLogic();
        } else {
          throw new Error('未找到executeLogic函数');
        }
      })();
    `;

    // 设置执行超时时间（30秒）
    const timeout = 30000;

    // 在VM中执行代码并等待结果
    const resultPromise = vm.runInContext(wrappedCode, context, {
      timeout: timeout,
      displayErrors: true
    });

    // 等待Promise完成
    const result = await Promise.race([
      resultPromise,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('脚本执行超时')), timeout)
      )
    ]);

    return result;

  } catch (error) {
    if (error.message.includes('Script execution timed out') || error.message.includes('脚本执行超时')) {
      throw new Error('脚本执行超时，请检查是否存在无限循环或长时间运行的操作');
    }
    throw new Error(`脚本执行错误: ${error.message}`);
  }
};

module.exports = {
  executeScript
};
